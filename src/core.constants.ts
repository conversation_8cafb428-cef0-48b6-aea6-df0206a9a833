export enum CollectionStatus {
  PREMARKET = "PREMARKET",
  MARKET = "MARKET",
  DELETED = "DELETED",
}

export interface Collection {
  id: string;
  name: string;
  description: string;
  status: CollectionStatus;
  launchedAt?: Date;
  floorPrice: number; // Minimum floor price for collection items in TON
}

export const COLLECTION_STATUS_TEXT = {
  [CollectionStatus.PREMARKET]: "Pre-market",
  [CollectionStatus.MARKET]: "Market",
  [CollectionStatus.DELETED]: "Deleted",
};

export enum Role {
  ADMIN = "admin",
  USER = "user",
}

export interface UserBalance {
  sum: number;
  locked: number;
}

export interface UserEntity {
  id: string;
  name?: string;
  email?: string | null;
  displayName?: string | null;
  photoURL?: string | null;
  role?: "admin" | "user";
  tg_id?: string;
  ton_wallet_address?: string;
  referral_id?: string; // Telegram ID of the user who referred this user
  referral_fee?: number; // Custom referral fee in BPS (basis points)
  balance?: UserBalance;
}

export enum OrderStatus {
  ACTIVE = "active",
  PAID = "paid",
  GIFT_SENT_TO_RELAYER = "gift_sent_to_relayer",
  FULFILLED = "fulfilled",
  CANCELLED = "cancelled",
}

export interface OrderEntity {
  id?: string;
  number?: number; // Auto-incremented order number
  buyerId?: string; // Optional since orders can be created without a buyer
  sellerId: string;
  collectionId: string; // Collection ID for floor price validation
  amount: number;
  status: OrderStatus;
  deadline?: Date; // Deadline for seller to fulfill the order
  giftSentToRelayerAt?: Date; // When gift was sent to relayer
  createdAt?: Date;
  updatedAt?: Date;
}

export interface TxLookup {
  last_checked_record_id: string;
}
