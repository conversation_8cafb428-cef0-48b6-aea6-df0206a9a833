import dotenv from "dotenv";
import { Telegraf } from "telegraf";
import { businessConnectionMiddleware } from "./middleware/business-connection";
import {
  handleCompleteOrderButton,
  handleEchoGiftButton,
  handleGetMyOrdersButton,
  handleGetReferralLinkButton,
  handleReceiveGiftButton,
} from "./handlers/buttons";
import {
  handleBackToMenuCallback,
  handleBackToOrdersCallback,
  handleCancelEchoModeCallback,
  handleContactSupportCallback,
  handleOpenMarketplaceCallback,
  handleOrderCompletionCallback,
  handleOrderHelpCallback,
  handleOrderSelectionCallback,
  handleReceiveGiftCallback,
} from "./handlers/callbacks";
import { handleHelpCommand, handleStartCommand } from "./handlers/commands";
import { handleMessage } from "./handlers/messages";

dotenv.config();

const BOT_TOKEN = process.env.BOT_TOKEN;

if (!BOT_TOKEN) {
  throw new Error("BOT_TOKEN is required in environment variables");
}

const bot = new Telegraf(BOT_TOKEN);

bot.use(businessConnectionMiddleware);

bot.start(handleStartCommand);
bot.help(handleHelpCommand);

bot.hears("🎁 Echo Gift", handleEchoGiftButton);
bot.hears("📋 Get My Orders", handleGetMyOrdersButton);
bot.hears("🎁 Receive Gift", handleReceiveGiftButton);
bot.hears("✅ Complete Order", handleCompleteOrderButton);
bot.hears("🔗 Get Referral Link", handleGetReferralLinkButton);

bot.action("order_help", handleOrderHelpCallback);
bot.action("contact_support", handleContactSupportCallback);
bot.action("open_marketplace", handleOpenMarketplaceCallback);
bot.action("cancel_echo_mode", handleCancelEchoModeCallback);
bot.action(/^order_(.+)$/, handleOrderSelectionCallback);
bot.action("back_to_orders", handleBackToOrdersCallback);
bot.action(/^complete_(.+)$/, handleOrderCompletionCallback);
bot.action(/^receive_gift_(.+)$/, handleReceiveGiftCallback);
bot.action("back_to_menu", handleBackToMenuCallback);

// Handle regular messages
bot.on("message", handleMessage);

bot.catch((err, ctx) => {
  console.error("Bot error:", err);
  ctx?.reply?.("Sorry, something went wrong. Please try again later.");
});

// Graceful shutdown handlers
process.once("SIGINT", () => {
  console.log("Received SIGINT, stopping bot...");
  bot.stop("SIGINT");
});

process.once("SIGTERM", () => {
  console.log("Received SIGTERM, stopping bot...");
  bot.stop("SIGTERM");
});

export default bot;
