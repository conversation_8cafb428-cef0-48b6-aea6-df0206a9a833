"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  TrendingUp,
  Hammer,
  Gift,
  Building,
  Camera,
  ShoppingBag,
} from "lucide-react";
import { useRouter, usePathname } from "next/navigation";

export default function MarketplaceFooter() {
  const router = useRouter();
  const pathname = usePathname();

  const navItems = [
    {
      icon: TrendingUp,
      label: "Market",
      href: "/marketplace",
      active: pathname === "/marketplace",
    },
    {
      icon: Hammer,
      label: "Auctions",
      href: "/auctions",
      active: pathname === "/auctions",
    },
    {
      icon: Gift,
      label: "My Gifts",
      href: "/gifts",
      active: pathname === "/gifts",
    },
    {
      icon: Building,
      label: "GiFi",
      href: "/gifi",
      active: pathname === "/gifi",
    },
    {
      icon: Camera,
      label: "Gallery",
      href: "/gallery",
      active: pathname === "/gallery",
    },
    {
      icon: ShoppingBag,
      label: "Orders",
      href: "/orders",
      active: pathname === "/orders",
    },
  ];

  return (
    <footer className="bg-slate-800 text-white border-t border-slate-700">
      <div className="flex items-center justify-around py-3">
        {navItems.map((item) => {
          const IconComponent = item.icon;
          return (
            <Button
              key={item.href}
              variant="ghost"
              onClick={() => router.push(item.href)}
              className={`flex flex-col items-center gap-1 p-2 h-auto ${
                item.active ? "text-blue-400" : "text-gray-400 hover:text-white"
              }`}
            >
              <IconComponent className="w-6 h-6" />
              <span className="text-xs font-medium">{item.label}</span>
            </Button>
          );
        })}
      </div>
    </footer>
  );
}
