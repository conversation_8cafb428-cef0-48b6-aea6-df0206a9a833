"use client";

import { useState, useEffect } from "react";
import { useRootContext } from "@/root-context";
import { OrderEntity, OrderStatus } from "@/core.constants";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Gift, Clock, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { httpsCallable } from "firebase/functions";
import { functions } from "@/root-context";

interface GetUserOrdersResponse {
  success: boolean;
  orders: OrderEntity[];
  count: number;
  userId: string;
}

export default function OrdersPage() {
  const { currentUser } = useRootContext();
  const [orders, setOrders] = useState<OrderEntity[]>([]);
  const [loading, setLoading] = useState(true);
  const [sendingGift, setSendingGift] = useState<string | null>(null);

  useEffect(() => {
    if (currentUser?.id) {
      fetchOrders();
    }
  }, [currentUser]);

  const fetchOrders = async () => {
    if (!currentUser?.id) return;

    try {
      setLoading(true);
      const getUserOrders = httpsCallable<{ userId: string }, GetUserOrdersResponse>(
        functions,
        "getUserOrders"
      );
      
      const result = await getUserOrders({ userId: currentUser.id });
      
      if (result.data.success) {
        setOrders(result.data.orders);
      } else {
        toast.error("Failed to fetch orders");
      }
    } catch (error) {
      console.error("Error fetching orders:", error);
      toast.error("Failed to fetch orders");
    } finally {
      setLoading(false);
    }
  };

  const handleSendGiftToRelayer = async (orderId: string) => {
    try {
      setSendingGift(orderId);
      
      const sendGiftToRelayer = httpsCallable<{ orderId: string }, any>(
        functions,
        "sendGiftToRelayer"
      );
      
      const result = await sendGiftToRelayer({ orderId });
      
      if (result.data.success) {
        toast.success("Gift sent to relayer! Buyer will be notified.");
        // Refresh orders to show updated status
        await fetchOrders();
      } else {
        toast.error(result.data.message || "Failed to send gift to relayer");
      }
    } catch (error: any) {
      console.error("Error sending gift to relayer:", error);
      toast.error(error.message || "Failed to send gift to relayer");
    } finally {
      setSendingGift(null);
    }
  };

  const getStatusIcon = (status: OrderStatus) => {
    switch (status) {
      case OrderStatus.ACTIVE:
        return <Clock className="h-4 w-4" />;
      case OrderStatus.PAID:
        return <AlertCircle className="h-4 w-4" />;
      case OrderStatus.GIFT_SENT_TO_RELAYER:
        return <Gift className="h-4 w-4" />;
      case OrderStatus.FULFILLED:
        return <CheckCircle className="h-4 w-4" />;
      case OrderStatus.CANCELLED:
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case OrderStatus.ACTIVE:
        return "bg-yellow-100 text-yellow-800";
      case OrderStatus.PAID:
        return "bg-orange-100 text-orange-800";
      case OrderStatus.GIFT_SENT_TO_RELAYER:
        return "bg-purple-100 text-purple-800";
      case OrderStatus.FULFILLED:
        return "bg-green-100 text-green-800";
      case OrderStatus.CANCELLED:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const sellerOrders = orders.filter(order => order.sellerId === currentUser?.id);
  const buyerOrders = orders.filter(order => order.buyerId === currentUser?.id);
  const paidSellerOrders = sellerOrders.filter(order => order.status === OrderStatus.PAID);

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-white">Loading orders...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-900 text-white p-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">My Orders</h1>
        
        <Tabs defaultValue="seller" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="seller">
              As Seller ({sellerOrders.length})
            </TabsTrigger>
            <TabsTrigger value="buyer">
              As Buyer ({buyerOrders.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="seller" className="mt-6">
            <div className="space-y-4">
              {paidSellerOrders.length > 0 && (
                <Card className="bg-orange-50 border-orange-200">
                  <CardHeader>
                    <CardTitle className="text-orange-800 flex items-center gap-2">
                      <AlertCircle className="h-5 w-5" />
                      Orders Ready for Gift Sending ({paidSellerOrders.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="text-orange-700">
                    You have {paidSellerOrders.length} paid order(s) waiting for gift delivery to relayer.
                  </CardContent>
                </Card>
              )}
              
              {sellerOrders.length === 0 ? (
                <Card>
                  <CardContent className="text-center py-8">
                    <p className="text-gray-500">No orders as seller yet.</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid gap-4">
                  {sellerOrders.map((order) => (
                    <Card key={order.id} className="bg-slate-800 border-slate-700">
                      <CardContent className="p-6">
                        <div className="flex justify-between items-start mb-4">
                          <div>
                            <h3 className="text-lg font-semibold">Order #{order.number}</h3>
                            <p className="text-gray-400">Amount: {order.amount} TON</p>
                            {order.buyerId && (
                              <p className="text-gray-400 text-sm">Buyer: {order.buyerId}</p>
                            )}
                          </div>
                          <Badge className={getStatusColor(order.status)}>
                            <div className="flex items-center gap-1">
                              {getStatusIcon(order.status)}
                              {order.status.replace('_', ' ').toUpperCase()}
                            </div>
                          </Badge>
                        </div>
                        
                        {order.status === OrderStatus.PAID && order.buyerId && (
                          <div className="mt-4">
                            <Button
                              onClick={() => handleSendGiftToRelayer(order.id!)}
                              disabled={sendingGift === order.id}
                              className="bg-purple-600 hover:bg-purple-700"
                            >
                              <Gift className="h-4 w-4 mr-2" />
                              {sendingGift === order.id ? "Sending..." : "Send Gift to Relayer"}
                            </Button>
                            <p className="text-sm text-gray-400 mt-2">
                              Click to mark gift as sent to relayer. The buyer will be notified.
                            </p>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="buyer" className="mt-6">
            <div className="space-y-4">
              {buyerOrders.length === 0 ? (
                <Card>
                  <CardContent className="text-center py-8">
                    <p className="text-gray-500">No orders as buyer yet.</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid gap-4">
                  {buyerOrders.map((order) => (
                    <Card key={order.id} className="bg-slate-800 border-slate-700">
                      <CardContent className="p-6">
                        <div className="flex justify-between items-start mb-4">
                          <div>
                            <h3 className="text-lg font-semibold">Order #{order.number}</h3>
                            <p className="text-gray-400">Amount: {order.amount} TON</p>
                            <p className="text-gray-400 text-sm">Seller: {order.sellerId}</p>
                          </div>
                          <Badge className={getStatusColor(order.status)}>
                            <div className="flex items-center gap-1">
                              {getStatusIcon(order.status)}
                              {order.status.replace('_', ' ').toUpperCase()}
                            </div>
                          </Badge>
                        </div>
                        
                        {order.status === OrderStatus.GIFT_SENT_TO_RELAYER && (
                          <div className="mt-4 p-3 bg-purple-900/20 border border-purple-500/30 rounded-lg">
                            <p className="text-purple-300 text-sm">
                              🎁 Gift has been sent to relayer! Check the Telegram bot to receive your gift.
                            </p>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
